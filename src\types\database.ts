export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      agencies: {
        Row: {
          code: string;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          id: string;
          location: string | null;
          name: string;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          code: string;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          location?: string | null;
          name: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          code?: string;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          location?: string | null;
          name?: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'agencies_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'agencies_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'agencies_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      agency_pics: {
        Row: {
          agency_id: string;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          id: string;
          joined_at: string | null;
          profile_id: string;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          agency_id: string;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          joined_at?: string | null;
          profile_id: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          agency_id?: string;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          joined_at?: string | null;
          profile_id?: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'agency_pics_agency_id_fkey';
            columns: ['agency_id'];
            isOneToOne: false;
            referencedRelation: 'agencies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'agency_pics_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'agency_pics_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'agency_pics_profile_id_fkey';
            columns: ['profile_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'agency_pics_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      complaints: {
        Row: {
          actual_completion_date: string | null;
          agency_id: string | null;
          cause_of_damage: string | null;
          contractor_company_id: string | null;
          contractor_company_name: string;
          correction_action: string | null;
          created_at: string | null;
          created_by: string | null;
          damage_complaint_date: string;
          damage_complaint_description: string;
          deleted_at: string | null;
          deleted_by: string | null;
          email: string;
          expected_completion_date: string;
          id: string;
          involves_mantrap: boolean;
          lift_id: string | null;
          location: string;
          no_pma_lif: string;
          number: string;
          proof_of_repair_urls: string[] | null;
          repair_completion_time: string | null;
          repair_cost_rm: number | null;
          status: Database['public']['Enums']['complaint_status'];
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          actual_completion_date?: string | null;
          agency_id?: string | null;
          cause_of_damage?: string | null;
          contractor_company_id?: string | null;
          contractor_company_name: string;
          correction_action?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          damage_complaint_date: string;
          damage_complaint_description: string;
          deleted_at?: string | null;
          deleted_by?: string | null;
          email: string;
          expected_completion_date: string;
          id?: string;
          involves_mantrap?: boolean;
          lift_id?: string | null;
          location: string;
          no_pma_lif: string;
          number: string;
          proof_of_repair_urls?: string[] | null;
          repair_completion_time?: string | null;
          repair_cost_rm?: number | null;
          status?: Database['public']['Enums']['complaint_status'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          actual_completion_date?: string | null;
          agency_id?: string | null;
          cause_of_damage?: string | null;
          contractor_company_id?: string | null;
          contractor_company_name?: string;
          correction_action?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          damage_complaint_date?: string;
          damage_complaint_description?: string;
          deleted_at?: string | null;
          deleted_by?: string | null;
          email?: string;
          expected_completion_date?: string;
          id?: string;
          involves_mantrap?: boolean;
          lift_id?: string | null;
          location?: string;
          no_pma_lif?: string;
          number?: string;
          proof_of_repair_urls?: string[] | null;
          repair_completion_time?: string | null;
          repair_cost_rm?: number | null;
          status?: Database['public']['Enums']['complaint_status'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'complaints_agency_id_fkey';
            columns: ['agency_id'];
            isOneToOne: false;
            referencedRelation: 'agencies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'complaints_contractor_company_id_fkey';
            columns: ['contractor_company_id'];
            isOneToOne: false;
            referencedRelation: 'contractor_companies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'complaints_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'complaints_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'complaints_lift_id_fkey';
            columns: ['lift_id'];
            isOneToOne: false;
            referencedRelation: 'lifts';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'complaints_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      contractor_companies: {
        Row: {
          code: string | null;
          company_hotline: string;
          company_name: string;
          company_type: Database['public']['Enums']['company_type'];
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          id: string;
          is_active: boolean;
          oem_name: string | null;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          code?: string | null;
          company_hotline: string;
          company_name: string;
          company_type: Database['public']['Enums']['company_type'];
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          is_active?: boolean;
          oem_name?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          code?: string | null;
          company_hotline?: string;
          company_name?: string;
          company_type?: Database['public']['Enums']['company_type'];
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          is_active?: boolean;
          oem_name?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'contractor_companies_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'contractor_companies_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'contractor_companies_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      contractor_pics: {
        Row: {
          agency_id: string | null;
          category: string[] | null;
          contractor_company_id: string;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          ic_number: string | null;
          joined_at: string | null;
          lif_list_file_urls: string[];
          official_email: string;
          profile_id: string;
          registration_cert_file_url: string;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          agency_id?: string | null;
          category?: string[] | null;
          contractor_company_id: string;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          ic_number?: string | null;
          joined_at?: string | null;
          lif_list_file_urls: string[];
          official_email: string;
          profile_id: string;
          registration_cert_file_url: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          agency_id?: string | null;
          category?: string[] | null;
          contractor_company_id?: string;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          ic_number?: string | null;
          joined_at?: string | null;
          lif_list_file_urls?: string[];
          official_email?: string;
          profile_id?: string;
          registration_cert_file_url?: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'contractor_pics_agency_id_fkey';
            columns: ['agency_id'];
            isOneToOne: false;
            referencedRelation: 'agencies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'contractor_pics_contractor_company_id_fkey';
            columns: ['contractor_company_id'];
            isOneToOne: false;
            referencedRelation: 'contractor_companies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'contractor_pics_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'contractor_pics_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'contractor_pics_profile_id_fkey';
            columns: ['profile_id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'contractor_pics_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      daily_logs: {
        Row: {
          contractor_company_id: string | null;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          description: string | null;
          id: string;
          lift_id: string | null;
          log_date: string;
          operation_log_type: string;
          person_in_charge_name: string;
          person_in_charge_phone: string;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          contractor_company_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          description?: string | null;
          id?: string;
          lift_id?: string | null;
          log_date: string;
          operation_log_type: string;
          person_in_charge_name: string;
          person_in_charge_phone: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          contractor_company_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          description?: string | null;
          id?: string;
          lift_id?: string | null;
          log_date?: string;
          operation_log_type?: string;
          person_in_charge_name?: string;
          person_in_charge_phone?: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'daily_logs_contractor_company_id_fkey';
            columns: ['contractor_company_id'];
            isOneToOne: false;
            referencedRelation: 'contractor_companies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'daily_logs_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'daily_logs_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'daily_logs_lift_id_fkey';
            columns: ['lift_id'];
            isOneToOne: false;
            referencedRelation: 'lifts';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'daily_logs_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      jkr_pics: {
        Row: {
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          notes: string | null;
          profile_id: string;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          notes?: string | null;
          profile_id: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          notes?: string | null;
          profile_id?: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'jkr_pics_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'jkr_pics_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'jkr_pics_profile_id_fkey';
            columns: ['profile_id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'jkr_pics_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      lifts: {
        Row: {
          brand: string | null;
          capacity_kg: number | null;
          capacity_persons: number | null;
          certificate_number: string | null;
          client_id: string | null;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          floors_served: number | null;
          id: string;
          installation_date: string | null;
          jkr_pic_id: string | null;
          last_inspection_date: string | null;
          lift_type: string | null;
          location: string | null;
          model: string | null;
          next_inspection_date: string | null;
          notes: string | null;
          serial_number: string | null;
          status: string | null;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          brand?: string | null;
          capacity_kg?: number | null;
          capacity_persons?: number | null;
          certificate_number?: string | null;
          client_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          floors_served?: number | null;
          id?: string;
          installation_date?: string | null;
          jkr_pic_id?: string | null;
          last_inspection_date?: string | null;
          lift_type?: string | null;
          location?: string | null;
          model?: string | null;
          next_inspection_date?: string | null;
          notes?: string | null;
          serial_number?: string | null;
          status?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          brand?: string | null;
          capacity_kg?: number | null;
          capacity_persons?: number | null;
          certificate_number?: string | null;
          client_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          floors_served?: number | null;
          id?: string;
          installation_date?: string | null;
          jkr_pic_id?: string | null;
          last_inspection_date?: string | null;
          lift_type?: string | null;
          location?: string | null;
          model?: string | null;
          next_inspection_date?: string | null;
          notes?: string | null;
          serial_number?: string | null;
          status?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lifts_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lifts_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lifts_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lifts_jkr_pic_id_fkey';
            columns: ['jkr_pic_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lifts_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      pmas: {
        Row: {
          contractor_id: string | null;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          file_url: string | null;
          id: string;
          pma_expiry_date: string;
          pma_status: Database['public']['Enums']['pma_status'];
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          contractor_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          file_url?: string | null;
          id?: string;
          pma_expiry_date: string;
          pma_status?: Database['public']['Enums']['pma_status'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          contractor_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          file_url?: string | null;
          id?: string;
          pma_expiry_date?: string;
          pma_status?: Database['public']['Enums']['pma_status'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'pmas_contractor_id_fkey';
            columns: ['contractor_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'pmas_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'pmas_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'pmas_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      profiles: {
        Row: {
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          email: string;
          id: string;
          name: string;
          onboarding_completed: boolean;
          phone_number: string | null;
          role: Database['public']['Enums']['user_role'];
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          email: string;
          id: string;
          name: string;
          onboarding_completed?: boolean;
          phone_number?: string | null;
          role: Database['public']['Enums']['user_role'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          email?: string;
          id?: string;
          name?: string;
          onboarding_completed?: boolean;
          phone_number?: string | null;
          role?: Database['public']['Enums']['user_role'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'profiles_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'profiles_deleted_by_fkey';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'profiles_updated_by_fkey';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      generate_complaint_number: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
    };
    Enums: {
      company_type: 'COMPETENT_FIRM' | 'NON_COMPETENT_FIRM' | 'OEM';
      complaint_status: 'open' | 'on_hold' | 'closed';
      pma_status: 'valid' | 'validating' | 'invalid';
      user_role: 'JKR' | 'Contractor' | 'Client';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      company_type: ['COMPETENT_FIRM', 'NON_COMPETENT_FIRM', 'OEM'],
      complaint_status: ['open', 'on_hold', 'closed'],
      pma_status: ['valid', 'validating', 'invalid'],
      user_role: ['JKR', 'Contractor', 'Client'],
    },
  },
} as const;
