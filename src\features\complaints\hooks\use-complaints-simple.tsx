import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useUserWithProfile } from '@/hooks/use-auth';
import { CreateComplaintInput } from '../schemas';
import { toast } from 'sonner';
import type { Database } from '@/types/database';

// Define the complaint data type with related tables for the query return
type ComplaintWithRelations = {
  id: string;
  email: string;
  number?: string;
  damage_complaint_date: string;
  expected_completion_date: string;
  contractor_company_name: string;
  location: string;
  no_pma_lif: string;
  damage_complaint_description: string;
  involves_mantrap: boolean;
  mantrap_location?: string;
  contact_number?: string;
  contractor_email?: string;
  contractor_contact_number?: string;
  actual_completion_date?: string;
  completion_notes?: string;
  proof_of_repair_urls?: string[];
  status: Database['public']['Enums']['complaint_status'];
  created_at: string;
  created_by?: string;
  agencies?: {
    id: string;
    name: string;
    code: string;
    location: string;
  } | null;
  contractor_companies?: {
    id: string;
    company_name: string;
    company_type: string;
    company_hotline: string;
    code: string;
  } | null;
  profiles?: {
    id: string;
    name: string;
    email: string;
  } | null;
};

// Hook to fetch all complaints with related data
export function useComplaints() {
  return useQuery({
    queryKey: ['complaints'],
    queryFn: async (): Promise<ComplaintWithRelations[]> => {
      const { data, error } = await supabase
        .from('complaints')
        .select(
          `
          *,
          agencies (
            id,
            name,
            code,
            location
          ),
          contractor_companies (
            id,
            company_name,
            company_type,
            company_hotline,
            code
          ),
          profiles!complaints_created_by_fkey (
            id,
            name,
            email
          )
        `,
        )
        .is('deleted_at', null)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create a new complaint
export function useCreateComplaint() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async (
      data: CreateComplaintInput & { proofOfRepairFiles?: File[] },
    ) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to create complaints');
      }

      // TODO: File upload handling will be implemented later
      // For now, we'll store file names as placeholder
      const proofFileUrls =
        data.proofOfRepairFiles?.map((file) => file.name) || [];

      // Generate complaint number
      const year = new Date().getFullYear();
      const complaintNumber = `DCL-${year}-${String(Math.floor(Math.random() * 9999) + 1).padStart(4, '0')}`; // Map UI status to database status
      const mapStatusToDb = (
        uiStatus?: string,
      ): Database['public']['Enums']['complaint_status'] => {
        switch (uiStatus) {
          case 'complete':
            return 'closed';
          case 'in progress':
            return 'on_hold';
          case 'pending':
          default:
            return 'open';
        }
      };
      const complaintData: any = {
        email: data.email,
        damage_complaint_date: data.damageComplaintDate
          .toISOString()
          .split('T')[0], // Convert to date string
        expected_completion_date: data.expectedCompletionDate
          .toISOString()
          .split('T')[0],
        contractor_company_name: data.contractorCompanyName,
        location: data.location,
        no_pma_lif: data.noPmaLif || '',
        damage_complaint_description:
          data.description || 'No description provided',
        involves_mantrap: !!data.mantrapLocation,
        mantrap_location: data.mantrapLocation || null,
        contact_number: data.contactNumber || null,
        contractor_email: data.contractorEmail || null,
        contractor_contact_number: data.contractorContactNumber || null,
        actual_completion_date: data.actualCompletionDate
          ? data.actualCompletionDate.toISOString().split('T')[0]
          : null,
        completion_notes: data.completionNotes || null,
        proof_of_repair_urls: proofFileUrls,
        status: mapStatusToDb(data.status),
        created_by: user.id,
        created_at: new Date().toISOString(),
        // TODO: Map agency name to agency_id when needed
        // agency_id: will need to lookup agency by name
      };

      const { data: result, error } = await supabase
        .from('complaints')
        .insert(complaintData)
        .select()
        .single();

      if (error) throw error;

      return {
        ...result,
        complaintNumber,
        proofFileUrls,
      };
    },
    onSuccess: (data) => {
      // Invalidate and refetch complaints
      queryClient.invalidateQueries({ queryKey: ['complaints'] });
      toast.success('Complaint created successfully!');
      console.log('Complaint created successfully:', data);
    },
    onError: (error: Error) => {
      console.error('Failed to create complaint:', error);
      toast.error(`Failed to create complaint: ${error.message}`);
    },
  });
}

// Hook to update an existing complaint
export function useUpdateComplaint() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: CreateComplaintInput & { proofOfRepairFiles?: File[] };
    }) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to update complaints');
      }

      // TODO: File upload handling will be implemented later
      // For now, we'll store file names as placeholder
      const proofFileUrls =
        data.proofOfRepairFiles?.map((file) => file.name) || [];

      // Map UI status to database status
      const mapStatusToDb = (
        uiStatus?: string,
      ): Database['public']['Enums']['complaint_status'] => {
        switch (uiStatus) {
          case 'complete':
            return 'closed';
          case 'in progress':
            return 'on_hold';
          case 'pending':
          default:
            return 'open';
        }
      };
      const complaintData = {
        email: data.email,
        damage_complaint_date: data.damageComplaintDate
          ?.toISOString()
          .split('T')[0],
        expected_completion_date: data.expectedCompletionDate
          ?.toISOString()
          .split('T')[0],
        contractor_company_name: data.contractorCompanyName,
        location: data.location,
        no_pma_lif: data.noPmaLif || '',
        damage_complaint_description:
          data.description || 'No description provided',
        involves_mantrap: !!data.mantrapLocation,
        mantrap_location: data.mantrapLocation || null,
        contact_number: data.contactNumber || null,
        contractor_email: data.contractorEmail || null,
        contractor_contact_number: data.contractorContactNumber || null,
        actual_completion_date: data.actualCompletionDate
          ? data.actualCompletionDate.toISOString().split('T')[0]
          : null,
        completion_notes: data.completionNotes || null,
        proof_of_repair_urls: proofFileUrls,
        status: mapStatusToDb(data.status),
        updated_at: new Date().toISOString(),
      };

      const { data: result, error } = await supabase
        .from('complaints')
        .update(complaintData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return {
        ...result,
        proofFileUrls,
      };
    },
    onSuccess: (data) => {
      // Invalidate and refetch complaints
      queryClient.invalidateQueries({ queryKey: ['complaints'] });
      toast.success('Complaint updated successfully!');
      console.log('Complaint updated successfully:', data);
    },
    onError: (error: Error) => {
      console.error('Failed to update complaint:', error);
      toast.error(`Failed to update complaint: ${error.message}`);
    },
  });
}
