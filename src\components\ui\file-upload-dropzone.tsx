'use client';

import * as React from 'react';
import { Upload, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileUploadDropzoneProps {
  onFilesChange: (files: File[]) => void;
  accept?: string;
  maxSize?: number; // in bytes
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  files?: File[]; // Controlled files prop
}

export function FileUploadDropzone({
  onFilesChange,
  accept = '.pdf,.jpg,.jpeg,.png',
  maxSize = 5 * 1024 * 1024, // 5MB
  maxFiles = 1,
  disabled = false,
  className,
  files, // Controlled files prop
}: FileUploadDropzoneProps) {
  const [dragActive, setDragActive] = React.useState(false);
  const [errors, setErrors] = React.useState<string[]>([]);
  const inputRef = React.useRef<HTMLInputElement>(null);

  const handleDrag = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleFiles = React.useCallback(
    (newFiles: FileList | File[]) => {
      const validateFile = (file: File): string | null => {
        if (file.size > maxSize) {
          return `File "${file.name}" is too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB.`;
        }

        const acceptedTypes = accept.split(',').map((type) => type.trim());
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
        const mimeType = file.type;

        const isValidType = acceptedTypes.some((type) => {
          if (type.startsWith('.')) {
            return fileExtension === type;
          }
          return mimeType.includes(type.replace('*', ''));
        });

        if (!isValidType) {
          return `File "${file.name}" has an invalid type. Accepted types: ${accept}`;
        }

        return null;
      };

      const fileArray = Array.from(newFiles);
      const newErrors: string[] = [];

      // Validate each file
      const validFiles: File[] = [];
      fileArray.forEach((file) => {
        const error = validateFile(file);
        if (error) {
          newErrors.push(error);
        } else {
          validFiles.push(file);
        }
      });

      // Check total file count - use controlled files length if available
      const currentFileCount = files ? files.length : 0;
      const totalFiles = currentFileCount + validFiles.length;
      if (totalFiles > maxFiles) {
        newErrors.push(`Maximum ${maxFiles} file(s) allowed.`);
        const allowedCount = maxFiles - currentFileCount;
        validFiles.splice(allowedCount);
      }

      setErrors(newErrors);

      if (validFiles.length > 0) {
        // In controlled mode, combine with existing files or replace if single file
        const finalFiles =
          maxFiles === 1 ? validFiles : [...(files || []), ...validFiles];
        onFilesChange(finalFiles);
      }
    },
    [files, maxFiles, onFilesChange, accept, maxSize],
  );

  const handleDrop = React.useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (disabled) return;

      const droppedFiles = e.dataTransfer.files;
      if (droppedFiles && droppedFiles.length > 0) {
        handleFiles(droppedFiles);
      }
    },
    [handleFiles, disabled],
  );

  const handleInputChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      e.preventDefault();
      if (disabled) return;

      const selectedFiles = e.target.files;
      if (selectedFiles && selectedFiles.length > 0) {
        handleFiles(selectedFiles);
      }

      // Reset the input value to allow selecting the same file again
      if (e.target) {
        e.target.value = '';
      }
    },
    [handleFiles, disabled],
  );

  const openFileDialog = React.useCallback(
    (e?: React.MouseEvent) => {
      if (disabled) return;
      e?.preventDefault();
      e?.stopPropagation();
      inputRef.current?.click();
    },
    [disabled],
  );

  return (
    <div className={cn('w-full', className)}>
      {/* Drop Zone */}
      <div
        className={cn(
          'relative border-2 border-dashed rounded-2xl p-12 transition-all duration-300 group',
          dragActive
            ? 'border-primary bg-primary/8 scale-[1.02] shadow-lg shadow-primary/20'
            : 'border-border hover:border-primary/60 hover:bg-muted/40 hover:shadow-md',
          disabled && 'opacity-50 cursor-not-allowed',
          !disabled && 'cursor-pointer',
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={inputRef}
          type="file"
          multiple={maxFiles > 1}
          accept={accept}
          onChange={handleInputChange}
          disabled={disabled}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          onClick={(e) => e.stopPropagation()}
        />

        <div className="flex flex-col items-center justify-center space-y-6 min-h-[140px]">
          <div
            className={cn(
              'p-6 rounded-2xl transition-all duration-300 group-hover:scale-110',
              dragActive ? 'bg-primary/20 shadow-lg' : 'bg-primary/10',
            )}
          >
            <Upload
              className={cn(
                'w-10 h-10 transition-all duration-300',
                dragActive
                  ? 'text-primary scale-110'
                  : 'text-primary/80 group-hover:text-primary',
              )}
            />
          </div>

          <div className="text-center space-y-4">
            <div className="space-y-2">
              <p className="text-lg font-semibold text-foreground">
                Drop {maxFiles === 1 ? 'your file' : 'files'} here
              </p>
              <p className="text-base text-muted-foreground">
                or{' '}
                <span className="text-primary font-medium underline underline-offset-2 hover:text-primary/80 transition-colors">
                  browse files
                </span>
              </p>
            </div>
            <div className="space-y-2 text-sm text-muted-foreground bg-muted/30 rounded-lg p-4 border">
              <p className="font-medium text-foreground">File Requirements:</p>
              <div className="space-y-1">
                <p>• Formats: {accept.replace(/\./g, '').toUpperCase()}</p>
                <p>
                  • Max size: {Math.round(maxSize / 1024 / 1024)}MB per file
                </p>
                {maxFiles > 1 && <p>• Up to {maxFiles} files allowed</p>}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="mt-4 space-y-2">
          {errors.map((error, index) => (
            <div
              key={index}
              className="flex items-center space-x-2 text-sm text-destructive"
            >
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
